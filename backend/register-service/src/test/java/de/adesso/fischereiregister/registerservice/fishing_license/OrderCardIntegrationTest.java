package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.card_orders.persistence.OrderStatus;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CardOrderStatus;
import org.openapitools.model.CardOrderStatusRequest;
import org.openapitools.model.ConsentInfo;
import org.openapitools.model.CreateRegularFishingLicenseRequest;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.Tax;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.UUID;

import java.util.List;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class OrderCardIntegrationTest {

    private static final String ENDPOINT = "/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders";
    private static final String ORDER_STATUS_ENDPOINT = "/orders/{orderId}/status";

    @Autowired
    private MockMvc mvc;

    @Autowired
    private CardOrderRepository cardOrderRepository;

    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 4XX status code if no content is provided.
            """)
    void orderCardWithoutData() throws Exception {

        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", "registerEntryId")
                .replaceFirst("\\{licenseId}", "licenseId");

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content("")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());

    }


    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 200 if a valid request is provided
            """)
    void orderCardValidRequest() throws Exception {
        final String registerEntryId = "351031b4-d8ca-4380-a49f-47159ac68b8b";
        final String licenseId = "SH85598631191522";
        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", registerEntryId)
                .replaceFirst("\\{licenseId}", licenseId);

        //final OrderCheckCardRequest request = new OrderCheckCardRequest();
        final CreateRegularFishingLicenseRequest request = new CreateRegularFishingLicenseRequest();

        request.setPerson(TestDataUtil.createPersonWithAddressApi());
        request.consentInfo(TestDataUtil.createConsentInfoApi());
        request.fees(TestDataUtil.createFeesApi());
        request.taxes(TestDataUtil.createTaxesApi());

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        final RegisterEntry registerEntry = new RegisterEntry();
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber(licenseId);
        registerEntry.getFishingLicenses().add(fishingLicense);

        registerEntryView.setData(registerEntry);

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size() + 2)))  //taxes plus we expect new CARD and new PDF, license document
                .andExpect(jsonPath("$.person.firstname", Matchers.containsString(request.getPerson().getFirstname())))
                .andExpect(jsonPath("$.fishingLicense.type", Matchers.containsString(LicenseType.REGULAR.toString())))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));
    }

    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 401 you try to file taxes for a different state
            """)
    void orderCardInvalidTax() throws Exception {
        final String registerEntryId = "351031b4-d8ca-4380-a49f-47159ac68b8b";
        final String licenseId = "SH85-5986-3119-1522";
        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", registerEntryId)
                .replaceFirst("\\{licenseId}", licenseId);

       // final OrderCheckCardRequest request = new OrderCheckCardRequest();
        final CreateRegularFishingLicenseRequest request = new CreateRegularFishingLicenseRequest();

        request.setPerson(TestDataUtil.createPersonApiWithAddressApi());
        request.consentInfo(new ConsentInfo());
        request.fees(TestDataUtil.createFeesApi());

        final List<Tax> taxes = TestDataUtil.createTaxesApi();
        taxes.get(0).setFederalState(FederalStateAbbreviation.BE);
        request.taxes(taxes);

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that the order status update endpoint returns 200 OK when updating with valid data
            """)
    void updateOrderStatusValidRequest() throws Exception {
        // Create a test card order
        final UUID orderId = UUID.randomUUID();
        final CardOrder cardOrder = createTestCardOrder(orderId);
        cardOrderRepository.save(cardOrder);

        // Create status update request
        final CardOrderStatusRequest request = new CardOrderStatusRequest();
        request.setStatus(CardOrderStatus.IN_PRODUCTION);
        request.setStatusNote("Order is now in production");
        request.setUID("test-uid-12345");

        final String interpolatedPath = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId.toString());

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").doesNotExist()); // Verify empty response body

        // Verify the order was updated in the database
        final CardOrder updatedOrder = cardOrderRepository.findById(orderId).orElse(null);
        assert updatedOrder != null;
        assert updatedOrder.getStatus() == OrderStatus.IN_PRODUCTION;
        assert "Order is now in production".equals(updatedOrder.getStatusNote());
    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that the order status update endpoint returns 400 with error details when order does not exist
            """)
    void updateOrderStatusOrderNotFound() throws Exception {
        final UUID nonExistentOrderId = UUID.randomUUID();

        final CardOrderStatusRequest request = new CardOrderStatusRequest();
        request.setStatus(CardOrderStatus.PRODUCED);
        request.setStatusNote("Order completed");
        request.setUID("test-uid-67890");

        final String interpolatedPath = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", nonExistentOrderId.toString());

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorDescription").value("Card order not found"))
                .andExpect(jsonPath("$.traceId").exists());
    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that the order status update endpoint returns 400 when orderId is not a valid UUID
            """)
    void updateOrderStatusInvalidOrderId() throws Exception {
        final String invalidOrderId = "not-a-valid-uuid";

        final CardOrderStatusRequest request = new CardOrderStatusRequest();
        request.setStatus(CardOrderStatus.SHIPPED);
        request.setStatusNote("Order shipped");
        request.setUID("test-uid-11111");

        final String interpolatedPath = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", invalidOrderId);

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorDescription").exists())
                .andExpect(jsonPath("$.traceId").exists());
    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that the order status update endpoint returns 400 when request body is empty
            """)
    void updateOrderStatusEmptyRequestBody() throws Exception {
        final UUID orderId = UUID.randomUUID();
        final CardOrder cardOrder = createTestCardOrder(orderId);
        cardOrderRepository.save(cardOrder);

        final String interpolatedPath = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId.toString());

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + interpolatedPath)
                        .content("")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorDescription").exists())
                .andExpect(jsonPath("$.traceId").exists());
    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that different status transitions work correctly
            """)
    void updateOrderStatusDifferentStatuses() throws Exception {
        // Test TRANSMITTED -> INFORMATION
        final UUID orderId1 = UUID.randomUUID();
        final CardOrder cardOrder1 = createTestCardOrder(orderId1);
        cardOrder1.setStatus(OrderStatus.TRANSMITTED);
        cardOrderRepository.save(cardOrder1);

        CardOrderStatusRequest request1 = new CardOrderStatusRequest();
        request1.setStatus(CardOrderStatus.INFORMATION);
        request1.setStatusNote("Additional information required");
        request1.setUID("uid-info-123");

        String path1 = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId1.toString());
        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + path1)
                        .content(asJsonString(request1))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").doesNotExist()); // Verify empty response body

        // Test IN_PRODUCTION -> PRODUCED
        final UUID orderId2 = UUID.randomUUID();
        final CardOrder cardOrder2 = createTestCardOrder(orderId2);
        cardOrder2.setStatus(OrderStatus.IN_PRODUCTION);
        cardOrderRepository.save(cardOrder2);

        CardOrderStatusRequest request2 = new CardOrderStatusRequest();
        request2.setStatus(CardOrderStatus.PRODUCED);
        request2.setStatusNote("Production completed successfully");
        request2.setUID("uid-produced-456");

        String path2 = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId2.toString());
        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + path2)
                        .content(asJsonString(request2))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").doesNotExist()); // Verify empty response body
    }

    @Test
    @DisplayName("""
            PUT /api/orders/{orderId}/status
            Verify that error status updates work correctly (UNPRODUCEABLE, UNDELIVERABLE)
            """)
    void updateOrderStatusErrorStatuses() throws Exception {
        // Test UNPRODUCEABLE status
        final UUID orderId1 = UUID.randomUUID();
        final CardOrder cardOrder1 = createTestCardOrder(orderId1);
        cardOrder1.setStatus(OrderStatus.IN_PRODUCTION);
        cardOrderRepository.save(cardOrder1);

        CardOrderStatusRequest request1 = new CardOrderStatusRequest();
        request1.setStatus(CardOrderStatus.UNPRODUCEABLE);
        request1.setStatusNote("Technical issue prevents production");
        request1.setUID("uid-error-123");

        String path1 = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId1.toString());
        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + path1)
                        .content(asJsonString(request1))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").doesNotExist()); // Verify empty response body

        // Test UNDELIVERABLE status
        final UUID orderId2 = UUID.randomUUID();
        final CardOrder cardOrder2 = createTestCardOrder(orderId2);
        cardOrder2.setStatus(OrderStatus.SHIPPED);
        cardOrderRepository.save(cardOrder2);

        CardOrderStatusRequest request2 = new CardOrderStatusRequest();
        request2.setStatus(CardOrderStatus.UNDELIVERABLE);
        request2.setStatusNote("Address not found");
        request2.setUID("uid-undeliverable-456");

        String path2 = ORDER_STATUS_ENDPOINT.replaceFirst("\\{orderId}", orderId2.toString());
        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080" + path2)
                        .content(asJsonString(request2))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    /**
     * Helper method to create a test CardOrder with default values
     */
    private CardOrder createTestCardOrder(UUID orderId) {
        CardOrder cardOrder = new CardOrder();
        cardOrder.setOrderId(orderId);
        cardOrder.setLicenseNumber("SH12345678901234");
        cardOrder.setIdentificationDocumentId("test-doc-id-" + orderId.toString().substring(0, 8));
        cardOrder.setStatus(OrderStatus.TRANSMITTED);
        cardOrder.setStatusNote("Initial status");
        cardOrder.setUpdatedAt(LocalDateTime.now());
        return cardOrder;
    }

}
